'use client'

import React, { createContext, useContext, useState, useRef, useEffect } from 'react'
import { Howl } from 'howler'

interface AudioTrack {
  id: string
  name: string
  nameHi: string
  url: string
  duration?: number
}

interface AudioContextType {
  isPlaying: boolean
  currentTrack: AudioTrack | null
  volume: number
  tracks: AudioTrack[]
  playTrack: (track: AudioTrack) => void
  pauseAudio: () => void
  resumeAudio: () => void
  stopAudio: () => void
  setVolume: (volume: number) => void
  toggleMute: () => void
  isMuted: boolean
}

const AudioContext = createContext<AudioContextType | undefined>(undefined)

// Using online spiritual audio sources as fallback
const defaultTracks: AudioTrack[] = [
  {
    id: 'om-namah-shivaya',
    name: 'Om <PERSON>ah <PERSON>',
    nameHi: 'ॐ नमः शिवाय',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Fallback audio
  },
  {
    id: 'gayatri-mantra',
    name: '<PERSON><PERSON><PERSON>',
    nameHi: 'गायत्री मंत्र',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Fallback audio
  },
  {
    id: 'temple-bells',
    name: 'Temple Bells',
    nameHi: 'मंदिर की घंटियां',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Fallback audio
  },
  {
    id: 'spiritual-ambient',
    name: 'Spiritual Ambient',
    nameHi: 'आध्यात्मिक वातावरण',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Fallback audio
  },
  {
    id: 'hanuman-chalisa',
    name: 'Hanuman Chalisa',
    nameHi: 'हनुमान चालीसा',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Fallback audio
  },
]

export const AudioProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTrack, setCurrentTrack] = useState<AudioTrack | null>(null)
  const [volume, setVolumeState] = useState(0.3)
  const [isMuted, setIsMuted] = useState(false)
  const [tracks] = useState<AudioTrack[]>(defaultTracks)

  const howlRef = useRef<Howl | null>(null)
  const previousVolume = useRef(0.3)

  useEffect(() => {
    // Cleanup on unmount
    return () => {
      if (howlRef.current) {
        howlRef.current.unload()
      }
    }
  }, [])

  const playTrack = (track: AudioTrack) => {
    try {
      // Stop current track if playing
      if (howlRef.current) {
        howlRef.current.stop()
        howlRef.current.unload()
      }

      // Create new Howl instance with better error handling
      howlRef.current = new Howl({
        src: [track.url],
        loop: true,
        volume: isMuted ? 0 : volume,
        html5: true, // Use HTML5 Audio for better compatibility
        onload: () => {
          console.log('Audio loaded successfully:', track.name)
        },
        onplay: () => {
          setIsPlaying(true)
          setCurrentTrack(track)
          console.log('Playing:', track.name)
        },
        onpause: () => {
          setIsPlaying(false)
        },
        onstop: () => {
          setIsPlaying(false)
        },
        onloaderror: (id, error) => {
          console.error('Audio load error for', track.name, ':', error)
          setIsPlaying(false)
          setCurrentTrack(null)
          // Show user-friendly error message
          alert(`Unable to load audio: ${track.name}. Please check your internet connection.`)
        },
        onplayerror: (id, error) => {
          console.error('Audio play error for', track.name, ':', error)
          setIsPlaying(false)
          // Try to unlock audio context (common issue on mobile)
          if (howlRef.current) {
            howlRef.current.once('unlock', () => {
              howlRef.current?.play()
            })
          }
        },
      })

      // Attempt to play
      const playPromise = howlRef.current.play()
      if (playPromise !== undefined) {
        // Handle play promise for better browser compatibility
        if (typeof playPromise === 'object' && playPromise.catch) {
          playPromise.catch((error: any) => {
            console.error('Play promise rejected:', error)
            setIsPlaying(false)
          })
        }
      }
    } catch (error) {
      console.error('Error in playTrack:', error)
      setIsPlaying(false)
      setCurrentTrack(null)
    }
  }

  const pauseAudio = () => {
    if (howlRef.current && isPlaying) {
      howlRef.current.pause()
    }
  }

  const resumeAudio = () => {
    if (howlRef.current && !isPlaying) {
      howlRef.current.play()
    }
  }

  const stopAudio = () => {
    if (howlRef.current) {
      howlRef.current.stop()
      setCurrentTrack(null)
    }
  }

  const setVolume = (newVolume: number) => {
    setVolumeState(newVolume)
    if (howlRef.current) {
      howlRef.current.volume(isMuted ? 0 : newVolume)
    }
    if (newVolume > 0 && isMuted) {
      setIsMuted(false)
    }
  }

  const toggleMute = () => {
    if (isMuted) {
      setIsMuted(false)
      if (howlRef.current) {
        howlRef.current.volume(volume)
      }
    } else {
      previousVolume.current = volume
      setIsMuted(true)
      if (howlRef.current) {
        howlRef.current.volume(0)
      }
    }
  }

  const value: AudioContextType = {
    isPlaying,
    currentTrack,
    volume,
    tracks,
    playTrack,
    pauseAudio,
    resumeAudio,
    stopAudio,
    setVolume,
    toggleMute,
    isMuted,
  }

  return <AudioContext.Provider value={value}>{children}</AudioContext.Provider>
}

export const useAudio = () => {
  const context = useContext(AudioContext)
  if (context === undefined) {
    throw new Error('useAudio must be used within an AudioProvider')
  }
  return context
}
