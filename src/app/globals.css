@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
  }

  body {
    @apply bg-white text-gray-900 antialiased;
  }
}

@layer components {
  .spiritual-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .spiritual-button {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-bhagwa-400 hover:bg-bhagwa-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-bhagwa-400 transition-all duration-200 hover:scale-105 active:scale-95;
  }

  .spiritual-button-secondary {
    @apply inline-flex items-center px-4 py-2 border border-bhagwa-400 text-sm font-medium rounded-md shadow-sm text-bhagwa-600 bg-white hover:bg-bhagwa-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-bhagwa-400 transition-all duration-200 hover:scale-105 active:scale-95;
  }

  .spiritual-input {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-bhagwa-400 focus:ring-bhagwa-400 sm:text-sm transition-all duration-200;
  }

  .spiritual-card {
    @apply bg-white rounded-lg shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 hover:border-bhagwa-200;
  }

  .spiritual-card-enhanced {
    @apply bg-gradient-to-br from-white to-bhagwa-50/30 rounded-xl shadow-lg p-6 border border-bhagwa-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 hover:border-bhagwa-300 hover:shadow-bhagwa-200/20;
  }

  .spiritual-heading {
    @apply text-3xl font-serif font-bold text-gray-900 mb-4;
  }

  .spiritual-subheading {
    @apply text-xl font-serif text-gray-700 mb-2;
  }

  .spiritual-text {
    @apply text-base text-gray-600 leading-relaxed;
  }

  .sacred-glow {
    @apply animate-sacred-glow;
  }

  .om-pulse {
    @apply animate-om-pulse;
  }

  .mandala-rotate {
    @apply animate-mandala-rotate;
  }

  .divine-shimmer {
    background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.4), transparent);
    background-size: 200% 100%;
    @apply animate-divine-shimmer;
  }

  .temple-bells {
    @apply animate-temple-bells;
  }

  .gradient-text {
    background: linear-gradient(135deg, #ff9933, #ff8800, #facc15);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .sacred-border {
    border-image: linear-gradient(45deg, #ff9933, #facc15, #a855f7) 1;
  }
}

/* Custom animations */
.diya-animation {
  animation: flicker 2s infinite;
}

.lotus-animation {
  animation: float 6s ease-in-out infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-bhagwa-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-bhagwa-500;
}