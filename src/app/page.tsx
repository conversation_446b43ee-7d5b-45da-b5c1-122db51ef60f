'use client'

import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import {
  OmIcon,
  LotusIcon,
  DiyaIcon,
  MandalaIcon,
  TempleIcon,
  YantraIcon
} from '@/components/icons/SacredIcons'
import AutoPlayAudio from '@/components/audio/AutoPlayAudio'

// Enhanced animated card component
const AnimatedCard = ({
  children,
  className,
  delay = 0
}: {
  children: React.ReactNode
  className?: string
  delay?: number
}) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    whileHover={{ y: -8, scale: 1.02 }}
    transition={{
      duration: 0.5,
      delay,
      hover: { duration: 0.3 }
    }}
    viewport={{ once: true }}
    className={className}
  >
    {children}
  </motion.div>
)

// Enhanced hero section with i18n
const HeroSection = () => {
  const { t } = useTranslation('home')

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Enhanced background with multiple layers */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/90 via-bhagwa-50/60 to-temple-gold-50/40 z-10" />
      <div className="absolute inset-0 bg-temple-pattern opacity-3" />

      {/* Floating sacred elements */}
      <div className="absolute top-20 left-10 opacity-20">
        <OmIcon size={80} className="text-bhagwa-400 om-pulse" />
      </div>
      <div className="absolute bottom-20 right-10 opacity-15">
        <LotusIcon size={100} className="text-lotus-pink-400 lotus-float" />
      </div>
      <div className="absolute top-1/3 right-1/4 opacity-10">
        <MandalaIcon size={120} className="text-divine-purple-300 mandala-rotate" />
      </div>

      <div className="relative z-20 spiritual-container text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
        >
          {/* Sacred symbol above title */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.5, duration: 0.8, type: "spring" }}
            className="mb-8"
          >
            <OmIcon size={60} className="text-bhagwa-500 mx-auto sacred-glow" />
          </motion.div>

          <h1 className="text-4xl md:text-6xl lg:text-7xl font-serif font-bold mb-6 gradient-text">
            {t('hero.title')}
          </h1>

          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.8 }}
            className="text-xl md:text-2xl text-gray-700 mb-8 max-w-4xl mx-auto leading-relaxed"
          >
            {t('hero.subtitle')}
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2, duration: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link href="/consultation" className="spiritual-button text-lg py-3 px-8 sacred-glow">
              {t('buttons.bookConsultation', { ns: 'common' })}
            </Link>
            <Link href="/services" className="spiritual-button-secondary text-lg py-3 px-8">
              {t('buttons.exploreServices', { ns: 'common' })}
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default function Home() {
  const { t } = useTranslation('home')

  const services = [
    {
      icon: DiyaIcon,
      title: t('services.dailyHoroscope.title'),
      description: t('services.dailyHoroscope.description'),
      color: 'text-bhagwa-500',
      bgColor: 'bg-bhagwa-50',
      href: '/horoscope'
    },
    {
      icon: YantraIcon,
      title: t('services.kundliAnalysis.title'),
      description: t('services.kundliAnalysis.description'),
      color: 'text-temple-gold-500',
      bgColor: 'bg-temple-gold-50',
      href: '/kundli'
    },
    {
      icon: OmIcon,
      title: t('services.liveConsultation.title'),
      description: t('services.liveConsultation.description'),
      color: 'text-divine-purple-500',
      bgColor: 'bg-divine-purple-50',
      href: '/consultation'
    },
    {
      icon: TempleIcon,
      title: t('services.pujaBooking.title'),
      description: t('services.pujaBooking.description'),
      color: 'text-bhagwa-600',
      bgColor: 'bg-bhagwa-100',
      href: '/puja'
    },
    {
      icon: LotusIcon,
      title: t('services.gemstoneConsultation.title'),
      description: t('services.gemstoneConsultation.description'),
      color: 'text-lotus-pink-600',
      bgColor: 'bg-lotus-pink-100',
      href: '/gemstones'
    },
    {
      icon: MandalaIcon,
      title: t('services.vastu.title'),
      description: t('services.vastu.description'),
      color: 'text-divine-purple-600',
      bgColor: 'bg-divine-purple-100',
      href: '/vastu'
    }
  ]

  return (
    <div className="min-h-screen">
      <AutoPlayAudio />
      <HeroSection />

      {/* Enhanced Services Section */}
      <section className="py-20 bg-gradient-to-b from-white to-bhagwa-50/30">
        <div className="spiritual-container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="spiritual-heading gradient-text text-center mb-4">
              {t('services.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t('services.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => {
              const IconComponent = service.icon
              return (
                <AnimatedCard
                  key={service.href}
                  delay={index * 0.1}
                  className="spiritual-card-enhanced group cursor-pointer"
                >
                  <Link href={service.href} className="block">
                    <div className={`${service.bgColor} w-16 h-16 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                      <IconComponent className={`w-8 h-8 ${service.color}`} />
                    </div>
                    <h3 className="spiritual-subheading group-hover:text-bhagwa-600 transition-colors">
                      {service.title}
                    </h3>
                    <p className="spiritual-text">
                      {service.description}
                    </p>
                    <div className="mt-4 text-bhagwa-500 font-medium text-sm opacity-0 group-hover:opacity-100 transition-opacity">
                      {t('buttons.learnMore', { ns: 'common' })} →
                    </div>
                  </Link>
                </AnimatedCard>
              )
            })}
          </div>
        </div>
      </section>

      {/* Enhanced Call to Action */}
      <section className="py-20 bg-gradient-to-br from-bhagwa-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10">
            <OmIcon size={100} className="text-bhagwa-400" />
          </div>
          <div className="absolute bottom-10 right-10">
            <LotusIcon size={120} className="text-lotus-pink-400" />
          </div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <MandalaIcon size={200} className="text-divine-purple-300 mandala-rotate" />
          </div>
        </div>

        <div className="spiritual-container text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="spiritual-heading gradient-text mb-8">
              {t('cta.title')}
            </h2>
            <p className="spiritual-text max-w-3xl mx-auto mb-8 text-lg">
              {t('cta.description')}
            </p>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link href="/register" className="spiritual-button text-lg py-3 px-8 sacred-glow">
                {t('buttons.startJourney', { ns: 'common' })}
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}