/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/consultation/page";
exports.ids = ["app/consultation/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fconsultation%2Fpage&page=%2Fconsultation%2Fpage&appPaths=%2Fconsultation%2Fpage&pagePath=private-next-app-dir%2Fconsultation%2Fpage.tsx&appDir=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fconsultation%2Fpage&page=%2Fconsultation%2Fpage&appPaths=%2Fconsultation%2Fpage&pagePath=private-next-app-dir%2Fconsultation%2Fpage.tsx&appDir=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'consultation',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/consultation/page.tsx */ \"(rsc)/./src/app/consultation/page.tsx\")), \"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/consultation/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/consultation/page\",\n        pathname: \"/consultation\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fconsultation%2Fpage&page=%2Fconsultation%2Fpage&appPaths=%2Fconsultation%2Fpage&pagePath=private-next-app-dir%2Fconsultation%2Fpage.tsx&appDir=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Noto_Sans_Devanagari%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22devanagari%22%5D%2C%22variable%22%3A%22--font-noto-sans-devanagari%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22notoSansDevanagari%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Noto_Serif_Devanagari%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22devanagari%22%5D%2C%22variable%22%3A%22--font-noto-serif-devanagari%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22notoSerifDevanagari%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Faudio%2FAudioControls.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Flayout%2FFooter.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Flayout%2FNavigation.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Fproviders%2FI18nProvider.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcontexts%2FAudioContext.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Noto_Sans_Devanagari%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22devanagari%22%5D%2C%22variable%22%3A%22--font-noto-sans-devanagari%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22notoSansDevanagari%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Noto_Serif_Devanagari%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22devanagari%22%5D%2C%22variable%22%3A%22--font-noto-serif-devanagari%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22notoSerifDevanagari%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Faudio%2FAudioControls.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Flayout%2FFooter.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Flayout%2FNavigation.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Fproviders%2FI18nProvider.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcontexts%2FAudioContext.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/audio/AudioControls.tsx */ \"(ssr)/./src/components/audio/AudioControls.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(ssr)/./src/components/layout/Footer.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Navigation.tsx */ \"(ssr)/./src/components/layout/Navigation.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/I18nProvider.tsx */ \"(ssr)/./src/components/providers/I18nProvider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AudioContext.tsx */ \"(ssr)/./src/contexts/AudioContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Noto_Sans_Devanagari%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22devanagari%22%5D%2C%22variable%22%3A%22--font-noto-sans-devanagari%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22notoSansDevanagari%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Noto_Serif_Devanagari%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22devanagari%22%5D%2C%22variable%22%3A%22--font-noto-serif-devanagari%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22notoSerifDevanagari%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Faudio%2FAudioControls.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Flayout%2FFooter.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Flayout%2FNavigation.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Fproviders%2FI18nProvider.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcontexts%2FAudioContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fapp%2Fconsultation%2Fpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fapp%2Fconsultation%2Fpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/consultation/page.tsx */ \"(ssr)/./src/app/consultation/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZwcmF2ZWVuc2luZ2glMkZEb3dubG9hZHMlMkZhc3Ryb2xvZ3klMkZzcmMlMkZhcHAlMkZjb25zdWx0YXRpb24lMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rYXNoaS12ZWRpYy1hc3Ryb2xvZ3kvPzE4NTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvcHJhdmVlbnNpbmdoL0Rvd25sb2Fkcy9hc3Ryb2xvZ3kvc3JjL2FwcC9jb25zdWx0YXRpb24vcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fapp%2Fconsultation%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/consultation/page.tsx":
/*!***************************************!*\
  !*** ./src/app/consultation/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst ConsultationPage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen pt-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gradient-to-br from-saffron-50 via-temple-gold-50/50 to-lotus-pink-50/30 relative overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spiritual-container text-center relative z-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-serif font-bold mb-6 gradient-text\",\n                                children: \"Live Consultation\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-700 mb-8 max-w-3xl mx-auto\",\n                                children: \"Connect with our expert astrologers for personalized guidance and spiritual insights\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spiritual-container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-serif font-bold mb-4 gradient-text\",\n                                children: \"Book Your Consultation\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-8\",\n                                children: \"Choose from our experienced astrologers\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"spiritual-button text-lg py-3 px-8 sacred-glow\",\n                                children: \"Book Now\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConsultationPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2NvbnN1bHRhdGlvbi9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRXlCO0FBQ2E7QUFHdEMsTUFBTUUsbUJBQTZCO0lBQ2pDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQVFELFdBQVU7MEJBQ2pCLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0gsaURBQU1BLENBQUNFLEdBQUc7d0JBQ1RHLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUdDLEdBQUc7d0JBQUc7d0JBQzdCQyxTQUFTOzRCQUFFRixTQUFTOzRCQUFHQyxHQUFHO3dCQUFFO3dCQUM1QkUsWUFBWTs0QkFBRUMsVUFBVTt3QkFBSTs7MENBRTVCLDhEQUFDQztnQ0FBR1IsV0FBVTswQ0FBK0Q7Ozs7OzswQ0FHN0UsOERBQUNTO2dDQUFFVCxXQUFVOzBDQUErQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPbEUsOERBQUNDO2dCQUFRRCxXQUFVOzBCQUNqQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ1U7Z0NBQUdWLFdBQVU7MENBQW1EOzs7Ozs7MENBQ2pFLDhEQUFDUztnQ0FBRVQsV0FBVTswQ0FBcUI7Ozs7OzswQ0FDbEMsOERBQUNXO2dDQUFPWCxXQUFVOzBDQUFpRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVEvRTtBQUVBLGlFQUFlRixnQkFBZ0JBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9rYXNoaS12ZWRpYy1hc3Ryb2xvZ3kvLi9zcmMvYXBwL2NvbnN1bHRhdGlvbi9wYWdlLnRzeD85MjA1Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuaW1wb3J0IHsgT21JY29uLCBMb3R1c0ljb24gfSBmcm9tICdAL2NvbXBvbmVudHMvaWNvbnMvU2FjcmVkSWNvbnMnXG5cbmNvbnN0IENvbnN1bHRhdGlvblBhZ2U6IFJlYWN0LkZDID0gKCkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIHB0LTIwXCI+XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0yMCBiZy1ncmFkaWVudC10by1iciBmcm9tLXNhZmZyb24tNTAgdmlhLXRlbXBsZS1nb2xkLTUwLzUwIHRvLWxvdHVzLXBpbmstNTAvMzAgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3Bpcml0dWFsLWNvbnRhaW5lciB0ZXh0LWNlbnRlciByZWxhdGl2ZSB6LTEwXCI+XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44IH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIG1kOnRleHQtNnhsIGZvbnQtc2VyaWYgZm9udC1ib2xkIG1iLTYgZ3JhZGllbnQtdGV4dFwiPlxuICAgICAgICAgICAgICBMaXZlIENvbnN1bHRhdGlvblxuICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1ncmF5LTcwMCBtYi04IG1heC13LTN4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgIENvbm5lY3Qgd2l0aCBvdXIgZXhwZXJ0IGFzdHJvbG9nZXJzIGZvciBwZXJzb25hbGl6ZWQgZ3VpZGFuY2UgYW5kIHNwaXJpdHVhbCBpbnNpZ2h0c1xuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG4gICAgICBcbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTE2IGJnLXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3Bpcml0dWFsLWNvbnRhaW5lclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LXNlcmlmIGZvbnQtYm9sZCBtYi00IGdyYWRpZW50LXRleHRcIj5Cb29rIFlvdXIgQ29uc3VsdGF0aW9uPC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItOFwiPkNob29zZSBmcm9tIG91ciBleHBlcmllbmNlZCBhc3Ryb2xvZ2VyczwvcD5cbiAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwic3Bpcml0dWFsLWJ1dHRvbiB0ZXh0LWxnIHB5LTMgcHgtOCBzYWNyZWQtZ2xvd1wiPlxuICAgICAgICAgICAgICBCb29rIE5vd1xuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuICAgIDwvZGl2PlxuICApXG59XG5cbmV4cG9ydCBkZWZhdWx0IENvbnN1bHRhdGlvblBhZ2VcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIm1vdGlvbiIsIkNvbnN1bHRhdGlvblBhZ2UiLCJkaXYiLCJjbGFzc05hbWUiLCJzZWN0aW9uIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5IiwiYW5pbWF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsImgxIiwicCIsImgyIiwiYnV0dG9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/consultation/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/audio/AudioControls.tsx":
/*!************************************************!*\
  !*** ./src/components/audio/AudioControls.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _contexts_AudioContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AudioContext */ \"(ssr)/./src/contexts/AudioContext.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_MusicalNoteIcon_PauseIcon_PlayIcon_SpeakerWaveIcon_SpeakerXMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,MusicalNoteIcon,PauseIcon,PlayIcon,SpeakerWaveIcon,SpeakerXMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_MusicalNoteIcon_PauseIcon_PlayIcon_SpeakerWaveIcon_SpeakerXMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,MusicalNoteIcon,PauseIcon,PlayIcon,SpeakerWaveIcon,SpeakerXMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SpeakerXMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_MusicalNoteIcon_PauseIcon_PlayIcon_SpeakerWaveIcon_SpeakerXMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,MusicalNoteIcon,PauseIcon,PlayIcon,SpeakerWaveIcon,SpeakerXMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SpeakerWaveIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_MusicalNoteIcon_PauseIcon_PlayIcon_SpeakerWaveIcon_SpeakerXMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,MusicalNoteIcon,PauseIcon,PlayIcon,SpeakerWaveIcon,SpeakerXMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MusicalNoteIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_MusicalNoteIcon_PauseIcon_PlayIcon_SpeakerWaveIcon_SpeakerXMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,MusicalNoteIcon,PauseIcon,PlayIcon,SpeakerWaveIcon,SpeakerXMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PauseIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_MusicalNoteIcon_PauseIcon_PlayIcon_SpeakerWaveIcon_SpeakerXMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,MusicalNoteIcon,PauseIcon,PlayIcon,SpeakerWaveIcon,SpeakerXMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst AudioControls = ()=>{\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(\"common\");\n    const { isPlaying, currentTrack, volume, tracks, playTrack, pauseAudio, resumeAudio, stopAudio, setVolume, toggleMute, isMuted } = (0,_contexts_AudioContext__WEBPACK_IMPORTED_MODULE_2__.useAudio)();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTrackList, setShowTrackList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handlePlayPause = ()=>{\n        if (isPlaying) {\n            pauseAudio();\n        } else if (currentTrack) {\n            resumeAudio();\n        } else {\n            // Play first track if none selected\n            playTrack(tracks[0]);\n        }\n    };\n    const handleTrackSelect = (track)=>{\n        playTrack(track);\n        setShowTrackList(false);\n    };\n    const handleVolumeChange = (e)=>{\n        const newVolume = parseFloat(e.target.value);\n        setVolume(newVolume);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20,\n                        scale: 0.9\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0,\n                        scale: 1\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: 20,\n                        scale: 0.9\n                    },\n                    className: \"mb-4 bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl border border-saffron-200 p-4 min-w-[280px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: t(\"audio.selectMantra\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowTrackList(!showTrackList),\n                                            className: \"p-1 rounded-lg hover:bg-saffron-50 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_MusicalNoteIcon_PauseIcon_PlayIcon_SpeakerWaveIcon_SpeakerXMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: `w-4 h-4 text-gray-500 transition-transform ${showTrackList ? \"rotate-180\" : \"\"}`\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                                    children: showTrackList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            height: 0\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            height: \"auto\"\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            height: 0\n                                        },\n                                        className: \"space-y-1 max-h-40 overflow-y-auto\",\n                                        children: tracks.map((track)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleTrackSelect(track),\n                                                className: `w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${currentTrack?.id === track.id ? \"bg-saffron-100 text-saffron-700\" : \"hover:bg-gray-50 text-gray-600\"}`,\n                                                children: i18n.language === \"hi\" ? track.nameHi : track.name\n                                            }, track.id, false, {\n                                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 23\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, undefined),\n                                currentTrack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-saffron-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-saffron-600 font-medium\",\n                                            children: [\n                                                t(\"audio.nowPlaying\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-saffron-800\",\n                                            children: i18n.language === \"hi\" ? currentTrack.nameHi : currentTrack.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: t(\"audio.volume\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: toggleMute,\n                                            className: \"p-1 rounded-lg hover:bg-saffron-50 transition-colors\",\n                                            children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_MusicalNoteIcon_PauseIcon_PlayIcon_SpeakerWaveIcon_SpeakerXMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_MusicalNoteIcon_PauseIcon_PlayIcon_SpeakerWaveIcon_SpeakerXMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 text-gray-500\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"0\",\n                                    max: \"1\",\n                                    step: \"0.1\",\n                                    value: volume,\n                                    onChange: handleVolumeChange,\n                                    className: \"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider\",\n                                    style: {\n                                        background: `linear-gradient(to right, #f97316 0%, #f97316 ${volume * 100}%, #e5e7eb ${volume * 100}%, #e5e7eb 100%)`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, undefined),\n                        currentTrack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: stopAudio,\n                            className: \"w-full py-2 px-4 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm font-medium transition-colors\",\n                            children: t(\"audio.stop\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                onClick: ()=>setIsExpanded(!isExpanded),\n                whileHover: {\n                    scale: 1.05\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                className: \"w-14 h-14 bg-gradient-saffron rounded-full shadow-lg flex items-center justify-center text-white hover:shadow-xl transition-shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_MusicalNoteIcon_PauseIcon_PlayIcon_SpeakerWaveIcon_SpeakerXMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, undefined),\n            currentTrack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                onClick: handlePlayPause,\n                whileHover: {\n                    scale: 1.05\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                className: \"absolute -top-2 -left-2 w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center border-2 border-saffron-500\",\n                children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_MusicalNoteIcon_PauseIcon_PlayIcon_SpeakerWaveIcon_SpeakerXMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4 text-saffron-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_MusicalNoteIcon_PauseIcon_PlayIcon_SpeakerWaveIcon_SpeakerXMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-saffron-600 ml-0.5\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AudioControls);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/audio/AudioControls.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/common/LanguageSwitcher.tsx":
/*!****************************************************!*\
  !*** ./src/components/common/LanguageSwitcher.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_LanguageIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,LanguageIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/LanguageIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_LanguageIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,LanguageIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst languages = [\n    {\n        code: \"en\",\n        name: \"English\",\n        nativeName: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n    },\n    {\n        code: \"hi\",\n        name: \"Hindi\",\n        nativeName: \"हिंदी\",\n        flag: \"\\uD83C\\uDDEE\\uD83C\\uDDF3\"\n    }\n];\nconst LanguageSwitcher = ({ variant = \"default\", className = \"\" })=>{\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const currentLanguage = languages.find((lang)=>lang.code === i18n.language) || languages[0];\n    const handleLanguageChange = async (languageCode)=>{\n        try {\n            await i18n.changeLanguage(languageCode);\n            setIsOpen(false);\n            // Store language preference\n            localStorage.setItem(\"preferred-language\", languageCode);\n            // Update document language attribute\n            document.documentElement.lang = languageCode;\n            // Update document direction for RTL languages if needed\n            document.documentElement.dir = languageCode === \"ar\" ? \"rtl\" : \"ltr\";\n        } catch (error) {\n            console.error(\"Failed to change language:\", error);\n        }\n    };\n    if (variant === \"icon-only\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `relative ${className}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsOpen(!isOpen),\n                    className: \"p-2 rounded-lg hover:bg-saffron-50 transition-colors\",\n                    \"aria-label\": \"Change language\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_LanguageIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-5 h-5 text-gray-600\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                    children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            scale: 0.95\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            scale: 1\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            scale: 0.95\n                        },\n                        className: \"absolute right-0 top-full mt-2 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-[150px] z-50\",\n                        children: languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleLanguageChange(language.code),\n                                className: `w-full px-4 py-2 text-left hover:bg-saffron-50 transition-colors flex items-center space-x-3 ${currentLanguage.code === language.code ? \"bg-saffron-50 text-saffron-700\" : \"text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: language.flag\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: language.nativeName\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, language.code, true, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (variant === \"compact\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `relative ${className}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsOpen(!isOpen),\n                    className: \"flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-saffron-50 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg\",\n                            children: currentLanguage.flag\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: currentLanguage.code.toUpperCase()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_LanguageIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: `w-4 h-4 text-gray-500 transition-transform ${isOpen ? \"rotate-180\" : \"\"}`\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                    children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -10,\n                            scale: 0.95\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0,\n                            scale: 1\n                        },\n                        exit: {\n                            opacity: 0,\n                            y: -10,\n                            scale: 0.95\n                        },\n                        className: \"absolute right-0 top-full mt-2 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-[150px] z-50\",\n                        children: languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleLanguageChange(language.code),\n                                className: `w-full px-4 py-2 text-left hover:bg-saffron-50 transition-colors flex items-center space-x-3 ${currentLanguage.code === language.code ? \"bg-saffron-50 text-saffron-700\" : \"text-gray-700\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: language.flag\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: language.nativeName\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, language.code, true, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Default variant\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-3 px-4 py-2 rounded-lg hover:bg-saffron-50 transition-colors border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg\",\n                        children: currentLanguage.flag\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-gray-700\",\n                                children: currentLanguage.nativeName\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500\",\n                                children: currentLanguage.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_LanguageIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: `w-4 h-4 text-gray-500 transition-transform ${isOpen ? \"rotate-180\" : \"\"}`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10,\n                        scale: 0.95\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0,\n                        scale: 1\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10,\n                        scale: 0.95\n                    },\n                    className: \"absolute right-0 top-full mt-2 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-[200px] z-50\",\n                    children: languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleLanguageChange(language.code),\n                            className: `w-full px-4 py-3 text-left hover:bg-saffron-50 transition-colors flex items-center space-x-3 ${currentLanguage.code === language.code ? \"bg-saffron-50 text-saffron-700\" : \"text-gray-700\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg\",\n                                    children: language.flag\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium\",\n                                            children: language.nativeName\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: language.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, language.code, true, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/components/common/LanguageSwitcher.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LanguageSwitcher);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/common/LanguageSwitcher.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/icons/SacredIcons.tsx":
/*!**********************************************!*\
  !*** ./src/components/icons/SacredIcons.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DiyaIcon: () => (/* binding */ DiyaIcon),\n/* harmony export */   LotusIcon: () => (/* binding */ LotusIcon),\n/* harmony export */   MandalaIcon: () => (/* binding */ MandalaIcon),\n/* harmony export */   OmIcon: () => (/* binding */ OmIcon),\n/* harmony export */   SwastikaIcon: () => (/* binding */ SwastikaIcon),\n/* harmony export */   TempleIcon: () => (/* binding */ TempleIcon),\n/* harmony export */   TrishulIcon: () => (/* binding */ TrishulIcon),\n/* harmony export */   YantraIcon: () => (/* binding */ YantraIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst OmIcon = ({ className = \"\", size = 24 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 100 100\",\n        className: className,\n        fill: \"currentColor\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M35.5 25.2c-4.8 0-8.7 3.9-8.7 8.7s3.9 8.7 8.7 8.7c4.8 0 8.7-3.9 8.7-8.7s-3.9-8.7-8.7-8.7zm0 14.4c-3.1 0-5.7-2.5-5.7-5.7s2.5-5.7 5.7-5.7 5.7 2.5 5.7 5.7-2.6 5.7-5.7 5.7z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 16,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M75.8 45.2c-2.1-7.1-8.4-12.2-15.9-12.2-6.2 0-11.6 3.4-14.4 8.4-1.4-2.1-3.8-3.5-6.5-3.5-4.3 0-7.8 3.5-7.8 7.8 0 1.8.6 3.4 1.6 4.7-3.2 2.8-5.2 6.9-5.2 11.5 0 8.4 6.8 15.2 15.2 15.2 3.9 0 7.4-1.5 10.1-3.9 2.7 2.4 6.2 3.9 10.1 3.9 8.4 0 15.2-6.8 15.2-15.2 0-3.1-.9-6-2.4-8.7z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 17,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"20\",\n                cy: \"75\",\n                r: \"8\",\n                opacity: \"0.7\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 18,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M85 70c0 8.3-6.7 15-15 15s-15-6.7-15-15 6.7-15 15-15 15 6.7 15 15zm-3 0c0-6.6-5.4-12-12-12s-12 5.4-12 12 5.4 12 12 12 12-5.4 12-12z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 19,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\nconst LotusIcon = ({ className = \"\", size = 24 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 100 100\",\n        className: className,\n        fill: \"currentColor\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M50 85c-2.8 0-5-2.2-5-5V65c0-2.8 2.2-5 5-5s5 2.2 5 5v15c0 2.8-2.2 5-5 5z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 31,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"45\",\n                rx: \"8\",\n                ry: \"15\",\n                transform: \"rotate(-30 50 45)\",\n                opacity: \"0.8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 32,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"45\",\n                rx: \"8\",\n                ry: \"15\",\n                transform: \"rotate(-60 50 45)\",\n                opacity: \"0.8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 33,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"45\",\n                rx: \"8\",\n                ry: \"15\",\n                transform: \"rotate(-90 50 45)\",\n                opacity: \"0.8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 34,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"45\",\n                rx: \"8\",\n                ry: \"15\",\n                transform: \"rotate(-120 50 45)\",\n                opacity: \"0.8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 35,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"45\",\n                rx: \"8\",\n                ry: \"15\",\n                transform: \"rotate(-150 50 45)\",\n                opacity: \"0.8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 36,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"45\",\n                rx: \"8\",\n                ry: \"15\",\n                transform: \"rotate(180 50 45)\",\n                opacity: \"0.8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"45\",\n                rx: \"8\",\n                ry: \"15\",\n                transform: \"rotate(150 50 45)\",\n                opacity: \"0.8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 38,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"45\",\n                rx: \"8\",\n                ry: \"15\",\n                transform: \"rotate(120 50 45)\",\n                opacity: \"0.8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 39,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"45\",\n                rx: \"8\",\n                ry: \"15\",\n                transform: \"rotate(90 50 45)\",\n                opacity: \"0.8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 40,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"45\",\n                rx: \"8\",\n                ry: \"15\",\n                transform: \"rotate(60 50 45)\",\n                opacity: \"0.8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 41,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"45\",\n                rx: \"8\",\n                ry: \"15\",\n                transform: \"rotate(30 50 45)\",\n                opacity: \"0.8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 42,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"45\",\n                rx: \"8\",\n                ry: \"15\",\n                opacity: \"0.8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 43,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"45\",\n                r: \"6\",\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 44,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined);\nconst TrishulIcon = ({ className = \"\", size = 24 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 100 100\",\n        className: className,\n        fill: \"currentColor\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"50\",\n                y1: \"20\",\n                x2: \"50\",\n                y2: \"85\",\n                stroke: \"currentColor\",\n                strokeWidth: \"3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 56,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M35 25c0-8.3 6.7-15 15-15s15 6.7 15 15-6.7 15-15 15-15-6.7-15-15z\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 57,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M30 20c-5.5 0-10-4.5-10-10s4.5-10 10-10\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 58,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M70 20c5.5 0 10-4.5 10-10s-4.5-10-10-10\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 59,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"45\",\n                cy: \"75\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 60,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"55\",\n                cy: \"75\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 61,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M42 80h16v5H42z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 62,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n        lineNumber: 49,\n        columnNumber: 3\n    }, undefined);\nconst SwastikaIcon = ({ className = \"\", size = 24 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 100 100\",\n        className: className,\n        fill: \"currentColor\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"45\",\n                y: \"20\",\n                width: \"10\",\n                height: \"60\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 74,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"20\",\n                y: \"45\",\n                width: \"60\",\n                height: \"10\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 75,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"55\",\n                y: \"20\",\n                width: \"25\",\n                height: \"10\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 76,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"20\",\n                y: \"20\",\n                width: \"10\",\n                height: \"25\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 77,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"20\",\n                y: \"70\",\n                width: \"25\",\n                height: \"10\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 78,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"70\",\n                y: \"55\",\n                width: \"10\",\n                height: \"25\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 79,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined);\nconst DiyaIcon = ({ className = \"\", size = 24 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 100 100\",\n        className: className,\n        fill: \"currentColor\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"70\",\n                rx: \"25\",\n                ry: \"15\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 91,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"65\",\n                rx: \"20\",\n                ry: \"10\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 92,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M50 45c-2 0-4 2-4 4v16h8V49c0-2-2-4-4-4z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 93,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                cx: \"50\",\n                cy: \"40\",\n                rx: \"6\",\n                ry: \"8\",\n                opacity: \"0.7\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 94,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M44 40c0-3.3 2.7-6 6-6s6 2.7 6 6-2.7 6-6 6-6-2.7-6-6z\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 95,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"35\",\n                r: \"2\",\n                opacity: \"0.9\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 96,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n        lineNumber: 84,\n        columnNumber: 3\n    }, undefined);\nconst MandalaIcon = ({ className = \"\", size = 24 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 100 100\",\n        className: className,\n        fill: \"currentColor\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"50\",\n                r: \"40\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1\",\n                opacity: \"0.3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 108,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"50\",\n                r: \"30\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1\",\n                opacity: \"0.5\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 109,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"50\",\n                r: \"20\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1\",\n                opacity: \"0.7\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 110,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"50\",\n                r: \"10\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1\",\n                opacity: \"0.9\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 111,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"50\",\n                r: \"5\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 112,\n                columnNumber: 5\n            }, undefined),\n            Array.from({\n                length: 8\n            }, (_, i)=>{\n                const angle = i * 45 * (Math.PI / 180);\n                const x1 = 50 + Math.cos(angle) * 15;\n                const y1 = 50 + Math.sin(angle) * 15;\n                const x2 = 50 + Math.cos(angle) * 35;\n                const y2 = 50 + Math.sin(angle) * 35;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: x1,\n                            y1: y1,\n                            x2: x2,\n                            y2: y2,\n                            stroke: \"currentColor\",\n                            strokeWidth: \"1\",\n                            opacity: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: x2,\n                            cy: y2,\n                            r: \"3\",\n                            opacity: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, i, true, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, undefined);\n            })\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n        lineNumber: 101,\n        columnNumber: 3\n    }, undefined);\nconst TempleIcon = ({ className = \"\", size = 24 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 100 100\",\n        className: className,\n        fill: \"currentColor\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"20\",\n                y: \"60\",\n                width: \"60\",\n                height: \"30\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 140,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"15\",\n                y: \"55\",\n                width: \"70\",\n                height: \"8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 141,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                points: \"50,20 25,55 75,55\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 142,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"45\",\n                y: \"65\",\n                width: \"10\",\n                height: \"20\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 143,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"30\",\n                y: \"65\",\n                width: \"8\",\n                height: \"15\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 144,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"62\",\n                y: \"65\",\n                width: \"8\",\n                height: \"15\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 145,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"40\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 146,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"47\",\n                y: \"15\",\n                width: \"6\",\n                height: \"10\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 147,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"12\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 148,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n        lineNumber: 133,\n        columnNumber: 3\n    }, undefined);\nconst YantraIcon = ({ className = \"\", size = 24 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 100 100\",\n        className: className,\n        fill: \"currentColor\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"10\",\n                y: \"10\",\n                width: \"80\",\n                height: \"80\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 160,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"50\",\n                r: \"35\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 161,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                points: \"50,25 65,60 35,60\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 162,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                points: \"50,75 35,40 65,40\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 163,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"50\",\n                cy: \"50\",\n                r: \"8\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 164,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"25\",\n                cy: \"25\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 165,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"75\",\n                cy: \"25\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 166,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"25\",\n                cy: \"75\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 167,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"75\",\n                cy: \"75\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n                lineNumber: 168,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/components/icons/SacredIcons.tsx\",\n        lineNumber: 153,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/icons/SacredIcons.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_icons_SacredIcons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/icons/SacredIcons */ \"(ssr)/./src/components/icons/SacredIcons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst footerNavigation = {\n    services: [\n        {\n            name: \"Daily Horoscope\",\n            href: \"/services/horoscope\"\n        },\n        {\n            name: \"Kundli Analysis\",\n            href: \"/services/kundli\"\n        },\n        {\n            name: \"Live Consultation\",\n            href: \"/services/consultation\"\n        },\n        {\n            name: \"Puja Booking\",\n            href: \"/services/puja\"\n        }\n    ],\n    company: [\n        {\n            name: \"About Us\",\n            href: \"/about\"\n        },\n        {\n            name: \"Our Astrologers\",\n            href: \"/astrologers\"\n        },\n        {\n            name: \"Testimonials\",\n            href: \"/testimonials\"\n        },\n        {\n            name: \"Contact\",\n            href: \"/contact\"\n        }\n    ],\n    legal: [\n        {\n            name: \"Privacy Policy\",\n            href: \"/privacy\"\n        },\n        {\n            name: \"Terms of Service\",\n            href: \"/terms\"\n        },\n        {\n            name: \"Refund Policy\",\n            href: \"/refund\"\n        }\n    ],\n    social: [\n        {\n            name: \"WhatsApp\",\n            href: \"https://wa.me/your-number\",\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: \"Facebook\",\n            href: \"#\",\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: \"Instagram\",\n            href: \"#\",\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ]\n};\nfunction Footer() {\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"common\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gradient-to-br from-gray-900 via-gray-800 to-saffron-900 text-white relative overflow-hidden\",\n        \"aria-labelledby\": \"footer-heading\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                id: \"footer-heading\",\n                className: \"sr-only\",\n                children: \"Footer\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-10 left-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_SacredIcons__WEBPACK_IMPORTED_MODULE_3__.OmIcon, {\n                            size: 80,\n                            className: \"text-saffron-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-10 right-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_SacredIcons__WEBPACK_IMPORTED_MODULE_3__.LotusIcon, {\n                            size: 100,\n                            className: \"text-lotus-pink-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_SacredIcons__WEBPACK_IMPORTED_MODULE_3__.TempleIcon, {\n                            size: 120,\n                            className: \"text-temple-gold-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 spiritual-container py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_SacredIcons__WEBPACK_IMPORTED_MODULE_3__.OmIcon, {\n                                                    className: \"w-8 h-8 text-saffron-400 om-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-serif font-bold gradient-text\",\n                                                            children: \"Kashi Vedic\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-saffron-300\",\n                                                            children: \"काशी वैदिक\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 mb-6 leading-relaxed\",\n                                            children: \"Connect with ancient Vedic wisdom through personalized astrology consultations and spiritual guidance.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: footerNavigation.social.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: item.href,\n                                                    className: \"w-10 h-10 bg-saffron-600 rounded-full flex items-center justify-center hover:bg-saffron-500 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                            className: \"h-5 w-5\",\n                                                            \"aria-hidden\": \"true\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-6 text-saffron-300\",\n                                            children: \"Services\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: footerNavigation.services.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: item.href,\n                                                        className: \"text-gray-300 hover:text-saffron-300 transition-colors duration-200\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, item.name, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-6 text-saffron-300\",\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: footerNavigation.company.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: item.href,\n                                                        className: \"text-gray-300 hover:text-saffron-300 transition-colors duration-200\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, item.name, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.3\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold mb-6 text-saffron-300\",\n                                            children: \"Legal\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3\",\n                                            children: footerNavigation.legal.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: item.href,\n                                                        className: \"text-gray-300 hover:text-saffron-300 transition-colors duration-200\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, item.name, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 pt-8 border-t border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                    initial: {\n                                        opacity: 0\n                                    },\n                                    whileInView: {\n                                        opacity: 1\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-gray-400 text-sm mb-4 md:mb-0\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" Kashi Vedic Astrology. All rights reserved. | Made with \\uD83D\\uDE4F for spiritual seekers\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0\n                                    },\n                                    whileInView: {\n                                        opacity: 1\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDD49️ Om Namah Shivaya \\uD83D\\uDD49️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/Navigation.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_common_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/common/LanguageSwitcher */ \"(ssr)/./src/components/common/LanguageSwitcher.tsx\");\n/* harmony import */ var _components_icons_SacredIcons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/icons/SacredIcons */ \"(ssr)/./src/components/icons/SacredIcons.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction Navigation() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(\"common\");\n    const navigation = [\n        {\n            name: t(\"navigation.home\"),\n            href: \"/\"\n        },\n        {\n            name: t(\"navigation.services\"),\n            href: \"/services\"\n        },\n        {\n            name: t(\"navigation.consultation\"),\n            href: \"/consultation\"\n        },\n        {\n            name: t(\"navigation.about\"),\n            href: \"/about\"\n        },\n        {\n            name: t(\"navigation.contact\"),\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed w-full z-50 bg-white/90 backdrop-blur-md border-b border-saffron-100 shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"spiritual-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_SacredIcons__WEBPACK_IMPORTED_MODULE_6__.OmIcon, {\n                                            className: \"w-8 h-8 text-saffron-500 om-pulse group-hover:scale-110 transition-transform duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-saffron-500/20 rounded-full blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-serif font-bold text-gray-900 gradient-text\",\n                                            children: \"Kashi Vedic\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-saffron-600 font-medium -mt-1\",\n                                            children: \"काशी वैदिक\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-6\",\n                            children: [\n                                navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: `relative text-sm font-medium transition-all duration-200 hover:scale-105 ${pathname === item.href ? \"text-saffron-600\" : \"text-gray-600 hover:text-saffron-500\"}`,\n                                        children: [\n                                            item.name,\n                                            pathname === item.href && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                layoutId: \"activeTab\",\n                                                className: \"absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-saffron rounded-full\",\n                                                initial: false,\n                                                transition: {\n                                                    type: \"spring\",\n                                                    stiffness: 500,\n                                                    damping: 30\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.href, true, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    variant: \"compact\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/login\",\n                                    className: \"spiritual-button text-sm py-2 px-4 sacred-glow\",\n                                    children: t(\"buttons.login\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    variant: \"icon-only\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsOpen(!isOpen),\n                                    className: \"p-2 rounded-md text-gray-600 hover:text-saffron-500 hover:bg-saffron-50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"Open main menu\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: \"auto\"\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    className: \"md:hidden bg-white/95 backdrop-blur-md border-b border-saffron-100 shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spiritual-container py-6 space-y-4\",\n                        children: [\n                            navigation.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        delay: index * 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: `block text-base font-medium py-2 px-4 rounded-lg transition-all duration-200 ${pathname === item.href ? \"text-saffron-600 bg-saffron-50\" : \"text-gray-600 hover:text-saffron-500 hover:bg-saffron-50\"}`,\n                                        onClick: ()=>setIsOpen(false),\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    delay: navigation.length * 0.1\n                                },\n                                className: \"pt-4 border-t border-saffron-100\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/login\",\n                                    className: \"block spiritual-button text-center w-full\",\n                                    onClick: ()=>setIsOpen(false),\n                                    children: t(\"buttons.login\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/I18nProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/I18nProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nProvider: () => (/* binding */ I18nProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! i18next */ \"(ssr)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../public/locales/en/common.json */ \"(ssr)/./public/locales/en/common.json\");\n/* harmony import */ var _public_locales_hi_common_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../public/locales/hi/common.json */ \"(ssr)/./public/locales/hi/common.json\");\n/* harmony import */ var _public_locales_en_home_json__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../public/locales/en/home.json */ \"(ssr)/./public/locales/en/home.json\");\n/* harmony import */ var _public_locales_hi_home_json__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../public/locales/hi/home.json */ \"(ssr)/./public/locales/hi/home.json\");\n/* __next_internal_client_entry_do_not_use__ I18nProvider auto */ \n\n\n\n\n// Import translation files\n\n\n\n\n// Initialize i18next\ni18next__WEBPACK_IMPORTED_MODULE_3__[\"default\"].use(react_i18next__WEBPACK_IMPORTED_MODULE_2__.initReactI18next).init({\n    resources: {\n        en: {\n            common: _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_4__,\n            home: _public_locales_en_home_json__WEBPACK_IMPORTED_MODULE_6__\n        },\n        hi: {\n            common: _public_locales_hi_common_json__WEBPACK_IMPORTED_MODULE_5__,\n            home: _public_locales_hi_home_json__WEBPACK_IMPORTED_MODULE_7__\n        }\n    },\n    lng: \"en\",\n    fallbackLng: \"en\",\n    debug: \"development\" === \"development\",\n    interpolation: {\n        escapeValue: false\n    },\n    react: {\n        useSuspense: false\n    }\n});\nconst I18nProvider = ({ children })=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load saved language preference\n        const savedLanguage = localStorage.getItem(\"preferred-language\");\n        if (savedLanguage && [\n            \"en\",\n            \"hi\"\n        ].includes(savedLanguage)) {\n            i18next__WEBPACK_IMPORTED_MODULE_3__[\"default\"].changeLanguage(savedLanguage);\n            document.documentElement.lang = savedLanguage;\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_i18next__WEBPACK_IMPORTED_MODULE_2__.I18nextProvider, {\n        i18n: i18next__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/components/providers/I18nProvider.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/I18nProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AudioContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/AudioContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioProvider: () => (/* binding */ AudioProvider),\n/* harmony export */   useAudio: () => (/* binding */ useAudio)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var howler__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! howler */ \"(ssr)/./node_modules/howler/dist/howler.js\");\n/* harmony import */ var howler__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(howler__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ AudioProvider,useAudio auto */ \n\n\nconst AudioContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Using online spiritual audio sources as fallback\nconst defaultTracks = [\n    {\n        id: \"om-namah-shivaya\",\n        name: \"Om Namah Shivaya\",\n        nameHi: \"ॐ नमः शिवाय\",\n        url: \"https://www.soundjay.com/misc/sounds/bell-ringing-05.wav\"\n    },\n    {\n        id: \"gayatri-mantra\",\n        name: \"Gayatri Mantra\",\n        nameHi: \"गायत्री मंत्र\",\n        url: \"https://www.soundjay.com/misc/sounds/bell-ringing-05.wav\"\n    },\n    {\n        id: \"temple-bells\",\n        name: \"Temple Bells\",\n        nameHi: \"मंदिर की घंटियां\",\n        url: \"https://www.soundjay.com/misc/sounds/bell-ringing-05.wav\"\n    },\n    {\n        id: \"spiritual-ambient\",\n        name: \"Spiritual Ambient\",\n        nameHi: \"आध्यात्मिक वातावरण\",\n        url: \"https://www.soundjay.com/misc/sounds/bell-ringing-05.wav\"\n    },\n    {\n        id: \"hanuman-chalisa\",\n        name: \"Hanuman Chalisa\",\n        nameHi: \"हनुमान चालीसा\",\n        url: \"https://www.soundjay.com/misc/sounds/bell-ringing-05.wav\"\n    }\n];\nconst AudioProvider = ({ children })=>{\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTrack, setCurrentTrack] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [volume, setVolumeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0.3);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tracks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTracks);\n    const howlRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const previousVolume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0.3);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Cleanup on unmount\n        return ()=>{\n            if (howlRef.current) {\n                howlRef.current.unload();\n            }\n        };\n    }, []);\n    const playTrack = (track)=>{\n        try {\n            // Stop current track if playing\n            if (howlRef.current) {\n                howlRef.current.stop();\n                howlRef.current.unload();\n            }\n            // Create new Howl instance with better error handling\n            howlRef.current = new howler__WEBPACK_IMPORTED_MODULE_2__.Howl({\n                src: [\n                    track.url\n                ],\n                loop: true,\n                volume: isMuted ? 0 : volume,\n                html5: true,\n                onload: ()=>{\n                    console.log(\"Audio loaded successfully:\", track.name);\n                },\n                onplay: ()=>{\n                    setIsPlaying(true);\n                    setCurrentTrack(track);\n                    console.log(\"Playing:\", track.name);\n                },\n                onpause: ()=>{\n                    setIsPlaying(false);\n                },\n                onstop: ()=>{\n                    setIsPlaying(false);\n                },\n                onloaderror: (id, error)=>{\n                    console.error(\"Audio load error for\", track.name, \":\", error);\n                    setIsPlaying(false);\n                    setCurrentTrack(null);\n                    // Show user-friendly error message\n                    alert(`Unable to load audio: ${track.name}. Please check your internet connection.`);\n                },\n                onplayerror: (id, error)=>{\n                    console.error(\"Audio play error for\", track.name, \":\", error);\n                    setIsPlaying(false);\n                    // Try to unlock audio context (common issue on mobile)\n                    if (howlRef.current) {\n                        howlRef.current.once(\"unlock\", ()=>{\n                            howlRef.current?.play();\n                        });\n                    }\n                }\n            });\n            // Attempt to play\n            const playPromise = howlRef.current.play();\n            if (playPromise !== undefined) {\n                // Handle play promise for better browser compatibility\n                if (typeof playPromise === \"object\" && playPromise.catch) {\n                    playPromise.catch((error)=>{\n                        console.error(\"Play promise rejected:\", error);\n                        setIsPlaying(false);\n                    });\n                }\n            }\n        } catch (error) {\n            console.error(\"Error in playTrack:\", error);\n            setIsPlaying(false);\n            setCurrentTrack(null);\n        }\n    };\n    const pauseAudio = ()=>{\n        if (howlRef.current && isPlaying) {\n            howlRef.current.pause();\n        }\n    };\n    const resumeAudio = ()=>{\n        if (howlRef.current && !isPlaying) {\n            howlRef.current.play();\n        }\n    };\n    const stopAudio = ()=>{\n        if (howlRef.current) {\n            howlRef.current.stop();\n            setCurrentTrack(null);\n        }\n    };\n    const setVolume = (newVolume)=>{\n        setVolumeState(newVolume);\n        if (howlRef.current) {\n            howlRef.current.volume(isMuted ? 0 : newVolume);\n        }\n        if (newVolume > 0 && isMuted) {\n            setIsMuted(false);\n        }\n    };\n    const toggleMute = ()=>{\n        if (isMuted) {\n            setIsMuted(false);\n            if (howlRef.current) {\n                howlRef.current.volume(volume);\n            }\n        } else {\n            previousVolume.current = volume;\n            setIsMuted(true);\n            if (howlRef.current) {\n                howlRef.current.volume(0);\n            }\n        }\n    };\n    const value = {\n        isPlaying,\n        currentTrack,\n        volume,\n        tracks,\n        playTrack,\n        pauseAudio,\n        resumeAudio,\n        stopAudio,\n        setVolume,\n        toggleMute,\n        isMuted\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AudioContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/contexts/AudioContext.tsx\",\n        lineNumber: 206,\n        columnNumber: 10\n    }, undefined);\n};\nconst useAudio = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AudioContext);\n    if (context === undefined) {\n        throw new Error(\"useAudio must be used within an AudioProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AudioContext.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4cc666869a22\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8va2FzaGktdmVkaWMtYXN0cm9sb2d5Ly4vc3JjL2FwcC9nbG9iYWxzLmNzcz84Y2VmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNGNjNjY2ODY5YTIyXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/consultation/page.tsx":
/*!***************************************!*\
  !*** ./src/app/consultation/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/app/consultation/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_Devanagari_arguments_weight_400_500_600_700_subsets_devanagari_variable_font_noto_sans_devanagari_display_swap_variableName_notoSansDevanagari___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Noto_Sans_Devanagari\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"subsets\":[\"devanagari\"],\"variable\":\"--font-noto-sans-devanagari\",\"display\":\"swap\"}],\"variableName\":\"notoSansDevanagari\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans_Devanagari\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"subsets\\\":[\\\"devanagari\\\"],\\\"variable\\\":\\\"--font-noto-sans-devanagari\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansDevanagari\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_Devanagari_arguments_weight_400_500_600_700_subsets_devanagari_variable_font_noto_sans_devanagari_display_swap_variableName_notoSansDevanagari___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_Devanagari_arguments_weight_400_500_600_700_subsets_devanagari_variable_font_noto_sans_devanagari_display_swap_variableName_notoSansDevanagari___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Serif_Devanagari_arguments_weight_400_500_600_700_subsets_devanagari_variable_font_noto_serif_devanagari_display_swap_variableName_notoSerifDevanagari___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Noto_Serif_Devanagari\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"subsets\":[\"devanagari\"],\"variable\":\"--font-noto-serif-devanagari\",\"display\":\"swap\"}],\"variableName\":\"notoSerifDevanagari\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Noto_Serif_Devanagari\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"subsets\\\":[\\\"devanagari\\\"],\\\"variable\\\":\\\"--font-noto-serif-devanagari\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSerifDevanagari\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Serif_Devanagari_arguments_weight_400_500_600_700_subsets_devanagari_variable_font_noto_serif_devanagari_display_swap_variableName_notoSerifDevanagari___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Serif_Devanagari_arguments_weight_400_500_600_700_subsets_devanagari_variable_font_noto_serif_devanagari_display_swap_variableName_notoSerifDevanagari___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AudioContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AudioContext */ \"(rsc)/./src/contexts/AudioContext.tsx\");\n/* harmony import */ var _components_providers_I18nProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/I18nProvider */ \"(rsc)/./src/components/providers/I18nProvider.tsx\");\n/* harmony import */ var _components_audio_AudioControls__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/audio/AudioControls */ \"(rsc)/./src/components/audio/AudioControls.tsx\");\n/* harmony import */ var _components_layout_Navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/Navigation */ \"(rsc)/./src/components/layout/Navigation.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Kashi Vedic Astrology - Divine Guidance & Spiritual Wisdom | काशी वैदिक ज्योतिष\",\n    description: \"Discover your spiritual path with expert Vedic astrology consultations, daily horoscopes, and divine guidance. Connect with experienced astrologers for personalized insights into your life journey. | विशेषज्ञ वैदिक ज्योतिष परामर्श के साथ अपना आध्यात्मिक मार्ग खोजें।\",\n    keywords: \"Vedic astrology, Hindu astrology, spiritual consultation, daily horoscope, kundli, puja booking, temple services, वैदिक ज्योतिष, हिंदू ज्योतिष, आध्यात्मिक परामर्श, दैनिक राशिफल, कुंडली\",\n    openGraph: {\n        title: \"Kashi Vedic Astrology - Divine Guidance & Spiritual Wisdom\",\n        description: \"Connect with ancient Vedic wisdom through personalized astrology consultations and spiritual guidance.\",\n        type: \"website\",\n        locale: \"en_US\",\n        alternateLocale: \"hi_IN\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Sans_Devanagari_arguments_weight_400_500_600_700_subsets_devanagari_variable_font_noto_sans_devanagari_display_swap_variableName_notoSansDevanagari___WEBPACK_IMPORTED_MODULE_8___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Noto_Serif_Devanagari_arguments_weight_400_500_600_700_subsets_devanagari_variable_font_noto_serif_devanagari_display_swap_variableName_notoSerifDevanagari___WEBPACK_IMPORTED_MODULE_9___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-gradient-to-b from-white via-saffron-50/30 to-temple-gold-50/20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_I18nProvider__WEBPACK_IMPORTED_MODULE_3__.I18nProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AudioContext__WEBPACK_IMPORTED_MODULE_2__.AudioProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative min-h-screen\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 -z-10 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-white via-saffron-50/30 to-temple-gold-50/20\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-temple-pattern opacity-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 right-0 w-1/3 h-1/3 bg-om-symbol bg-no-repeat opacity-5 om-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-0 left-0 w-1/4 h-1/4 bg-lotus-pattern bg-no-repeat opacity-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1/4 left-1/4 w-32 h-32 bg-mandala-pattern bg-no-repeat opacity-2 mandala-rotate\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-1/3 right-1/3 w-24 h-24 bg-sacred-geometry bg-no-repeat opacity-3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 left-0 w-full h-full bg-gradient-radial from-saffron-100/10 via-transparent to-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navigation__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"relative z-10\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_audio_AudioControls__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/astrology/src/app/layout.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/audio/AudioControls.tsx":
/*!************************************************!*\
  !*** ./src/components/audio/AudioControls.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/components/audio/AudioControls.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/components/layout/Footer.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/layout/Navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/Navigation.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/components/layout/Navigation.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/providers/I18nProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/I18nProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   I18nProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/components/providers/I18nProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/components/providers/I18nProvider.tsx#I18nProvider`);


/***/ }),

/***/ "(rsc)/./src/contexts/AudioContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/AudioContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AudioProvider: () => (/* binding */ e0),
/* harmony export */   useAudio: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/contexts/AudioContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/contexts/AudioContext.tsx#AudioProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/astrology/src/contexts/AudioContext.tsx#useAudio`);


/***/ }),

/***/ "(ssr)/./public/locales/en/common.json":
/*!***************************************!*\
  !*** ./public/locales/en/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"buttons":{"bookConsultation":"Book Consultation","exploreServices":"Explore Services","startJourney":"Start Your Journey","chatNow":"Chat Now","callNow":"Call Now","login":"Login","register":"Register","learnMore":"Learn More","getStarted":"Get Started","viewAll":"View All","readMore":"Read More"},"navigation":{"home":"Home","services":"Services","consultation":"Consultation","about":"About","contact":"Contact","horoscope":"Horoscope","kundli":"Kundli","astrologers":"Astrologers","blog":"Blog"},"audio":{"play":"Play","pause":"Pause","volume":"Volume","mute":"Mute","unmute":"Unmute","selectMantra":"Select Mantra","backgroundAudio":"Background Audio","spiritualSounds":"Spiritual Sounds","nowPlaying":"Now Playing","stop":"Stop"},"language":{"english":"English","hindi":"हिंदी","selectLanguage":"Select Language","changeLanguage":"Change Language"},"common":{"loading":"Loading...","error":"Error","success":"Success","warning":"Warning","info":"Information","close":"Close","save":"Save","cancel":"Cancel","confirm":"Confirm","delete":"Delete","edit":"Edit","view":"View","share":"Share","download":"Download"},"mantras":{"omNamahShivaya":"Om Namah Shivaya","gayatriMantra":"Gayatri Mantra","mahamrityunjaya":"Mahamrityunjaya Mantra","hanuman":"Hanuman Chalisa","ganesh":"Ganesh Mantra","lakshmi":"Lakshmi Mantra","saraswati":"Saraswati Mantra"}}');

/***/ }),

/***/ "(ssr)/./public/locales/en/home.json":
/*!*************************************!*\
  !*** ./public/locales/en/home.json ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"hero":{"title":"Discover Your Divine Path","subtitle":"Connect with ancient Vedic wisdom through personalized astrology consultations, spiritual guidance, and divine blessings.","description":"Experience the profound wisdom of Hindu astrology with our expert astrologers who guide you through life\'s journey using time-tested Vedic principles."},"services":{"title":"Our Divine Services","subtitle":"Explore our comprehensive range of spiritual and astrological services","dailyHoroscope":{"title":"Daily Horoscope","description":"Get your personalized daily horoscope based on your birth chart and planetary positions."},"kundliAnalysis":{"title":"Kundli Analysis","description":"Detailed analysis of your birth chart with insights into your life path and destiny."},"liveConsultation":{"title":"Live Consultation","description":"Connect with experienced astrologers for personalized guidance and solutions."},"pujaBooking":{"title":"Puja Booking","description":"Book authentic Hindu pujas and rituals performed by experienced priests."},"gemstoneConsultation":{"title":"Gemstone Consultation","description":"Discover the right gemstones to enhance your spiritual energy and well-being."},"vastu":{"title":"Vastu Shastra","description":"Harmonize your living and working spaces with ancient Vastu principles."}},"cta":{"title":"Begin Your Spiritual Journey","description":"Join thousands of seekers who have found clarity and guidance through our divine services. Let us help you navigate life\'s path with ancient wisdom and modern understanding."},"features":{"authentic":{"title":"Authentic Vedic Wisdom","description":"Traditional Hindu astrology practices passed down through generations"},"experienced":{"title":"Expert Astrologers","description":"Certified and experienced astrologers with deep knowledge of Vedic sciences"},"personalized":{"title":"Personalized Guidance","description":"Customized solutions based on your unique birth chart and life circumstances"},"spiritual":{"title":"Spiritual Growth","description":"Holistic approach to personal development and spiritual enlightenment"}}}');

/***/ }),

/***/ "(ssr)/./public/locales/hi/common.json":
/*!***************************************!*\
  !*** ./public/locales/hi/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"buttons":{"bookConsultation":"परामर्श बुक करें","exploreServices":"सेवाएं देखें","startJourney":"अपनी यात्रा शुरू करें","chatNow":"अभी चैट करें","callNow":"अभी कॉल करें","login":"लॉगिन","register":"पंजीकरण","learnMore":"और जानें","getStarted":"शुरू करें","viewAll":"सभी देखें","readMore":"और पढ़ें"},"navigation":{"home":"होम","services":"सेवाएं","consultation":"परामर्श","about":"हमारे बारे में","contact":"संपर्क","horoscope":"राशिफल","kundli":"कुंडली","astrologers":"ज्योतिषी","blog":"ब्लॉग"},"audio":{"play":"चलाएं","pause":"रोकें","volume":"आवाज़","mute":"मूक","unmute":"आवाज़ चालू करें","selectMantra":"मंत्र चुनें","backgroundAudio":"पृष्ठभूमि ऑडियो","spiritualSounds":"आध्यात्मिक ध्वनियां","nowPlaying":"अब चल रहा है","stop":"बंद करें"},"language":{"english":"English","hindi":"हिंदी","selectLanguage":"भाषा चुनें","changeLanguage":"भाषा बदलें"},"common":{"loading":"लोड हो रहा है...","error":"त्रुटि","success":"सफलता","warning":"चेतावनी","info":"जानकारी","close":"बंद करें","save":"सेव करें","cancel":"रद्द करें","confirm":"पुष्टि करें","delete":"हटाएं","edit":"संपादित करें","view":"देखें","share":"साझा करें","download":"डाउनलोड करें"},"mantras":{"omNamahShivaya":"ॐ नमः शिवाय","gayatriMantra":"गायत्री मंत्र","mahamrityunjaya":"महामृत्युंजय मंत्र","hanuman":"हनुमान चालीसा","ganesh":"गणेश मंत्र","lakshmi":"लक्ष्मी मंत्र","saraswati":"सरस्वती मंत्र"}}');

/***/ }),

/***/ "(ssr)/./public/locales/hi/home.json":
/*!*************************************!*\
  !*** ./public/locales/hi/home.json ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = JSON.parse('{"hero":{"title":"अपना दिव्य मार्ग खोजें","subtitle":"व्यक्तिगत ज्योतिष परामर्श, आध्यात्मिक मार्गदर्शन और दिव्य आशीर्वाद के माध्यम से प्राचीन वैदिक ज्ञान से जुड़ें।","description":"हमारे विशेषज्ञ ज्योतिषियों के साथ हिंदू ज्योतिष के गहन ज्ञान का अनुभव करें जो समय-परीक्षित वैदिक सिद्धांतों का उपयोग करके जीवन की यात्रा में आपका मार्गदर्शन करते हैं।"},"services":{"title":"हमारी दिव्य सेवाएं","subtitle":"आध्यात्मिक और ज्योतिषीय सेवाओं की हमारी व्यापक श्रृंखला का अन्वेषण करें","dailyHoroscope":{"title":"दैनिक राशिफल","description":"अपनी जन्म कुंडली और ग्रहों की स्थिति के आधार पर अपना व्यक्तिगत दैनिक राशिफल प्राप्त करें।"},"kundliAnalysis":{"title":"कुंडली विश्लेषण","description":"आपके जीवन पथ और भाग्य की अंतर्दृष्टि के साथ आपकी जन्म कुंडली का विस्तृत विश्लेषण।"},"liveConsultation":{"title":"लाइव परामर्श","description":"व्यक्तिगत मार्गदर्शन और समाधान के लिए अनुभवी ज्योतिषियों से जुड़ें।"},"pujaBooking":{"title":"पूजा बुकिंग","description":"अनुभवी पुजारियों द्वारा किए गए प्रामाणिक हिंदू पूजा और अनुष्ठान बुक करें।"},"gemstoneConsultation":{"title":"रत्न परामर्श","description":"अपनी आध्यात्मिक ऊर्जा और कल्याण को बढ़ाने के लिए सही रत्नों की खोज करें।"},"vastu":{"title":"वास्तु शास्त्र","description":"प्राचीन वास्तु सिद्धांतों के साथ अपने रहने और काम करने के स्थानों को सामंजस्यपूर्ण बनाएं।"}},"cta":{"title":"अपनी आध्यात्मिक यात्रा शुरू करें","description":"हजारों साधकों में शामिल हों जिन्होंने हमारी दिव्य सेवाओं के माध्यम से स्पष्टता और मार्गदर्शन पाया है। प्राचीन ज्ञान और आधुनिक समझ के साथ जीवन के पथ पर नेविगेट करने में हमें आपकी मदद करने दें।"},"features":{"authentic":{"title":"प्रामाणिक वैदिक ज्ञान","description":"पीढ़ियों से चली आ रही पारंपरिक हिंदू ज्योतिष प्रथाएं"},"experienced":{"title":"विशेषज्ञ ज्योतिषी","description":"वैदिक विज्ञान के गहन ज्ञान के साथ प्रमाणित और अनुभवी ज्योतिषी"},"personalized":{"title":"व्यक्तिगत मार्गदर्शन","description":"आपकी अनूठी जन्म कुंडली और जीवन परिस्थितियों के आधार पर अनुकूलित समाधान"},"spiritual":{"title":"आध्यात्मिक विकास","description":"व्यक्तिगत विकास और आध्यात्मिक ज्ञान के लिए समग्र दृष्टिकोण"}}}');

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/howler","vendor-chunks/i18next","vendor-chunks/motion-dom","vendor-chunks/react-i18next","vendor-chunks/@heroicons","vendor-chunks/html-parse-stringify","vendor-chunks/@swc","vendor-chunks/motion-utils","vendor-chunks/void-elements"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fconsultation%2Fpage&page=%2Fconsultation%2Fpage&appPaths=%2Fconsultation%2Fpage&pagePath=private-next-app-dir%2Fconsultation%2Fpage.tsx&appDir=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();