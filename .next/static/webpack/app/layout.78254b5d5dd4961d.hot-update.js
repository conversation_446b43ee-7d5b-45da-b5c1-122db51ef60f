/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Noto_Sans_Devanagari%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22devanagari%22%5D%2C%22variable%22%3A%22--font-noto-sans-devanagari%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22notoSansDevanagari%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Noto_Serif_Devanagari%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22devanagari%22%5D%2C%22variable%22%3A%22--font-noto-serif-devanagari%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22notoSerifDevanagari%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Faudio%2FAudioControls.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Flayout%2FFooter.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Flayout%2FNavigation.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Fproviders%2FI18nProvider.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcontexts%2FAudioContext.tsx&server=false!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Noto_Sans_Devanagari%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22devanagari%22%5D%2C%22variable%22%3A%22--font-noto-sans-devanagari%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22notoSansDevanagari%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Noto_Serif_Devanagari%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22devanagari%22%5D%2C%22variable%22%3A%22--font-noto-serif-devanagari%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22notoSerifDevanagari%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Faudio%2FAudioControls.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Flayout%2FFooter.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Flayout%2FNavigation.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Fproviders%2FI18nProvider.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcontexts%2FAudioContext.tsx&server=false! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Noto_Sans_Devanagari\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"subsets\":[\"devanagari\"],\"variable\":\"--font-noto-sans-devanagari\",\"display\":\"swap\"}],\"variableName\":\"notoSansDevanagari\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans_Devanagari\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"subsets\\\":[\\\"devanagari\\\"],\\\"variable\\\":\\\"--font-noto-sans-devanagari\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansDevanagari\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Noto_Serif_Devanagari\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"subsets\":[\"devanagari\"],\"variable\":\"--font-noto-serif-devanagari\",\"display\":\"swap\"}],\"variableName\":\"notoSerifDevanagari\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Noto_Serif_Devanagari\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"subsets\\\":[\\\"devanagari\\\"],\\\"variable\\\":\\\"--font-noto-serif-devanagari\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSerifDevanagari\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/audio/AudioControls.tsx */ \"(app-pages-browser)/./src/components/audio/AudioControls.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(app-pages-browser)/./src/components/layout/Footer.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Navigation.tsx */ \"(app-pages-browser)/./src/components/layout/Navigation.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/I18nProvider.tsx */ \"(app-pages-browser)/./src/components/providers/I18nProvider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AudioContext.tsx */ \"(app-pages-browser)/./src/contexts/AudioContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Noto_Sans_Devanagari%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22devanagari%22%5D%2C%22variable%22%3A%22--font-noto-sans-devanagari%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22notoSansDevanagari%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Noto_Serif_Devanagari%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22devanagari%22%5D%2C%22variable%22%3A%22--font-noto-serif-devanagari%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22notoSerifDevanagari%22%7D&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Faudio%2FAudioControls.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Flayout%2FFooter.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Flayout%2FNavigation.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcomponents%2Fproviders%2FI18nProvider.tsx&modules=%2FUsers%2Fpraveensingh%2FDownloads%2Fastrology%2Fsrc%2Fcontexts%2FAudioContext.tsx&server=false!\n"));

/***/ })

});